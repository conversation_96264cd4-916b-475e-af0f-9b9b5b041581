# ChewyAI - Comprehensive Security Review & Patterns

## Security Architecture Overview

```mermaid
flowchart TD
    A[Frontend] --> B[Backend API Gateway]
    B --> C{Authentication Check}
    C -->|Valid JWT| D[Authorization Check]
    C -->|Invalid| E[Return 401]
    D -->|Authorized| F[Business Logic]
    D -->|Unauthorized| G[Return 403]
    F --> H[Supabase RLS Check]
    F --> I[External API Calls]
    H --> J[Database Operation]
    I --> K[OpenRouter/Stripe]
    
    subgraph "Security Layers"
        L[Rate Limiting]
        M[Input Validation]
        N[Credit Validation]
        O[CORS Protection]
    end
    
    B --> L
    B --> M
    F --> N
    B --> O
```

## 1. Authentication Flow

### Frontend Authentication Pattern
```typescript
// Frontend NEVER handles external service credentials
class AuthService {
  async login(email: string, password: string): Promise<AuthResult> {
    // Only communicates with your backend
    const response = await fetch('/api/auth/login', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ email, password })
    });
    
    if (response.ok) {
      const { token, user } = await response.json();
      // Store JWT securely (httpOnly cookie preferred)
      this.setAuthToken(token);
      return { success: true, user };
    }
    
    return { success: false, error: 'Invalid credentials' };
  }
  
  // All subsequent requests include JWT
  async makeAuthenticatedRequest(endpoint: string, options: RequestInit) {
    return fetch(`/api${endpoint}`, {
      ...options,
      headers: {
        ...options.headers,
        'Authorization': `Bearer ${this.getToken()}`
      }
    });
  }
}
```

### Backend Authentication Middleware
```typescript
const authenticateJWT = async (req: Request, res: Response, next: NextFunction) => {
  const authHeader = req.headers.authorization;
  const token = authHeader && authHeader.split(' ')[1];
  
  if (!token) {
    return res.status(401).json({ error: 'Access token required' });
  }
  
  try {
    const decoded = jwt.verify(token, process.env.JWT_SECRET!);
    req.user = decoded as JWTPayload;
    next();
  } catch (error) {
    return res.status(403).json({ error: 'Invalid token' });
  }
};
```

## 2. Input Validation & Sanitization

```typescript
import { body, validationResult } from 'express-validator';
import DOMPurify from 'isomorphic-dompurify';

// Comprehensive validation middleware
const validateStudySetCreation = [
  body('name')
    .trim()
    .isLength({ min: 1, max: 255 })
    .escape()
    .withMessage('Name must be 1-255 characters'),
  body('document_ids')
    .isArray({ min: 1, max: 10 })
    .withMessage('Must select 1-10 documents'),
  body('custom_prompt')
    .optional()
    .trim()
    .isLength({ max: 1000 })
    .customSanitizer((value) => DOMPurify.sanitize(value))
];

const handleValidationErrors = (req: Request, res: Response, next: NextFunction) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ 
      error: 'Validation failed', 
      details: errors.array() 
    });
  }
  next();
};
```

## 3. Authorization & Resource Access Control

```typescript
// Resource ownership verification
const verifyResourceOwnership = (resourceType: 'study_set' | 'document') => {
  return async (req: Request, res: Response, next: NextFunction) => {
    const userId = req.user.id;
    const resourceId = req.params.id;
    
    const { data, error } = await supabase
      .from(resourceType === 'study_set' ? 'study_sets' : 'documents')
      .select('user_id')
      .eq('id', resourceId)
      .eq('user_id', userId)
      .single();
    
    if (error || !data) {
      return res.status(404).json({ error: 'Resource not found' });
    }
    
    next();
  };
};

// Usage: Protect routes that access user resources
app.delete('/api/study-sets/:id', 
  authenticateJWT, 
  verifyResourceOwnership('study_set'), 
  deleteStudySet
);
```

## 4. Credit System Security

```typescript
class CreditService {
  // Atomic credit operations with transaction safety
  async deductCredits(userId: string, operationType: string, metadata?: any): Promise<boolean> {
    const cost = await this.getOperationCost(operationType);
    
    // Use Supabase RPC for atomic transaction
    const { data, error } = await supabase.rpc('deduct_credits', {
      user_id: userId,
      credits_to_deduct: cost,
      operation_type: operationType,
      metadata: metadata || {}
    });
    
    if (error || !data) {
      throw new CreditError('Insufficient credits or operation failed');
    }
    
    return true;
  }
  
  // Prevent credit manipulation
  async validateCreditOperation(userId: string, operationType: string): Promise<boolean> {
    const userCredits = await this.getUserCredits(userId);
    const requiredCredits = await this.getOperationCost(operationType);
    
    return userCredits >= requiredCredits;
  }
}

// Credit validation middleware
const creditCheck = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const operationType = req.body.operation_type || 'ai_generation';
    const isValid = await creditService.validateCreditOperation(req.user.id, operationType);
    
    if (!isValid) {
      return res.status(402).json({ 
        error: 'Insufficient credits',
        required: await creditService.getOperationCost(operationType),
        available: await creditService.getUserCredits(req.user.id)
      });
    }
    
    next();
  } catch (error) {
    res.status(500).json({ error: 'Credit validation failed' });
  }
};
```

## 5. Rate Limiting & DDoS Protection

```typescript
import rateLimit from 'express-rate-limit';
import slowDown from 'express-slow-down';

// Different limits for different operations
const generalLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // 100 requests per window
  message: 'Too many requests'
});

const aiGenerationLimit = rateLimit({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 50, // 50 AI generations per hour
  keyGenerator: (req) => req.user?.id || req.ip,
  message: 'AI generation rate limit exceeded'
});

const speedLimiter = slowDown({
  windowMs: 15 * 60 * 1000, // 15 minutes
  delayAfter: 50, // allow 50 requests per window at full speed
  delayMs: 500 // add 500ms delay per request after delayAfter
});

// Apply to routes
app.use('/api', generalLimit, speedLimiter);
app.use('/api/ai/*', aiGenerationLimit);
```

## 6. Secure External API Integration

```typescript
class SecureAPIService {
  private openRouter: OpenRouter;
  private stripe: Stripe;
  
  constructor() {
    // All API keys server-side only - NEVER exposed to frontend
    this.openRouter = new OpenRouter({
      apiKey: process.env.OPENROUTER_API_KEY!
    });
    
    this.stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
      apiVersion: '2023-10-16'
    });
  }
  
  // Sanitize data before sending to external APIs
  async generateContent(prompt: string, userId: string): Promise<any> {
    // Log for audit trail
    await this.logAPIUsage(userId, 'openrouter', 'generation');
    
    // Sanitize prompt to prevent injection
    const sanitizedPrompt = DOMPurify.sanitize(prompt);
    
    try {
      const response = await this.openRouter.chat.completions.create({
        model: 'google/gemini-2.5-pro',
        messages: [{ role: 'user', content: sanitizedPrompt }],
        max_tokens: 2000,
        temperature: 0.7
      });
      
      return response;
    } catch (error) {
      // Log errors without exposing internal details
      await this.logAPIError(userId, 'openrouter', error);
      throw new Error('Content generation failed');
    }
  }
  
  // Secure Stripe operations
  async createPaymentIntent(amount: number, customerId: string): Promise<any> {
    try {
      return await this.stripe.paymentIntents.create({
        amount,
        currency: 'usd',
        customer: customerId,
        // Add metadata for tracking
        metadata: {
          service: 'chewyai',
          type: 'credit_purchase'
        }
      });
    } catch (error) {
      await this.logAPIError(customerId, 'stripe', error);
      throw new Error('Payment processing failed');
    }
  }
}
```

## 7. Database Security (Supabase RLS)

```sql
-- Row Level Security Policies
CREATE POLICY "Users can only see their own data" ON documents
  FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can only modify their own study sets" ON study_sets
  FOR ALL USING (auth.uid() = user_id);

-- Credit transaction logging (append-only)
CREATE POLICY "Users can only read their own credit history" ON credit_transactions
  FOR SELECT USING (auth.uid() = user_id);

-- Prevent direct credit manipulation
CREATE POLICY "No direct credit updates" ON users
  FOR UPDATE USING (false); -- Force use of stored procedures

-- Stored procedure for atomic credit deduction
CREATE OR REPLACE FUNCTION deduct_credits(
  user_id UUID,
  credits_to_deduct INTEGER,
  operation_type TEXT,
  metadata JSONB DEFAULT '{}'
)
RETURNS BOOLEAN AS $$
DECLARE
  current_credits INTEGER;
BEGIN
  -- Get current credits with row lock
  SELECT credits_remaining INTO current_credits
  FROM users 
  WHERE id = user_id
  FOR UPDATE;
  
  -- Check if sufficient credits
  IF current_credits < credits_to_deduct THEN
    RETURN FALSE;
  END IF;
  
  -- Deduct credits
  UPDATE users 
  SET credits_remaining = credits_remaining - credits_to_deduct
  WHERE id = user_id;
  
  -- Log transaction
  INSERT INTO credit_transactions (user_id, credits_used, operation_type, description, metadata)
  VALUES (user_id, credits_to_deduct, operation_type, 'AI generation', metadata);
  
  RETURN TRUE;
END;
$$ LANGUAGE plpgsql;
```

## 8. Environment & Configuration Security

### Backend Environment Variables
```bash
# All sensitive credentials stay server-side
SUPABASE_URL=your_supabase_project_url
SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_KEY=your_supabase_service_role_key  # CRITICAL: Never expose
STRIPE_SECRET_KEY=your_stripe_secret_key            # CRITICAL: Never expose
STRIPE_WEBHOOK_SECRET=your_stripe_webhook_secret    # CRITICAL: Never expose
OPENROUTER_API_KEY=your_openrouter_api_key         # CRITICAL: Never expose
JWT_SECRET=your_jwt_secret_key                     # CRITICAL: Never expose
BCRYPT_SALT_ROUNDS=12
PORT=5000
NODE_ENV=production
```

### Frontend Environment Variables
```bash
# Frontend only knows about your backend
VITE_API_BASE_URL=https://your-domain.com/api
# NO OTHER CREDENTIALS - Everything goes through your backend
```

## 9. Security Middleware Stack

```typescript
// Complete security middleware setup
app.use(helmet()); // Security headers
app.use(cors({
  origin: process.env.FRONTEND_URL,
  credentials: true,
  optionsSuccessStatus: 200
}));

// Rate limiting
app.use('/api', generalLimit);
app.use('/api/ai', aiGenerationLimit);

// Body parsing with limits
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Security headers
app.use((req, res, next) => {
  res.setHeader('X-Content-Type-Options', 'nosniff');
  res.setHeader('X-Frame-Options', 'DENY');
  res.setHeader('X-XSS-Protection', '1; mode=block');
  next();
});

// Audit logging
app.use('/api', auditLogger);
```

## 10. Security Checklist

### ✅ Authentication & Authorization
- [x] JWT tokens with proper expiration (1 hour)
- [x] bcrypt with salt rounds ≥ 12
- [x] Resource ownership verification on all operations
- [x] Supabase Row Level Security policies
- [x] No authentication credentials in frontend

### ✅ Input Validation & Sanitization
- [x] Comprehensive input validation with express-validator
- [x] HTML sanitization with DOMPurify
- [x] File upload restrictions and validation
- [x] SQL injection prevention via parameterized queries

### ✅ API Security
- [x] Rate limiting per user and IP
- [x] CORS properly configured
- [x] Security headers (helmet.js)
- [x] Request size limits
- [x] Error messages don't leak sensitive data

### ✅ Business Logic Security
- [x] Atomic credit operations
- [x] Credit manipulation prevention
- [x] Audit logging for sensitive operations
- [x] External API call sanitization

### ✅ Infrastructure Security
- [x] HTTPS enforcement
- [x] Environment variables properly secured
- [x] No sensitive data in frontend code
- [x] Proper secret management

### ✅ Data Protection
- [x] Encryption in transit (HTTPS)
- [x] Encryption at rest (Supabase)
- [x] Personal data protection
- [x] Secure file storage (Supabase Storage)

## Key Security Principles

1. **Zero Trust Frontend**: Frontend never has access to external service credentials
2. **Layered Security**: Multiple validation layers at different levels
3. **Principle of Least Privilege**: Users only access their own resources
4. **Audit Everything**: Log all sensitive operations
5. **Fail Securely**: Default to deny access when in doubt
6. **Defense in Depth**: Multiple security controls for each threat