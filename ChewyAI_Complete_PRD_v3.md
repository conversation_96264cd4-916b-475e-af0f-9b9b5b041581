# ChewyAI - Complete Product Requirements Document (v3)

## Table of Contents

1.  [Executive Summary](#1-executive-summary)
2.  [System Architecture](#2-system-architecture)
3.  [Business Model & Pricing](#3-business-model--pricing)
4.  [Database Design](#4-database-design)
5.  [API Specification](#5-api-specification)
6.  [Frontend Technical Specification](#6-frontend-technical-specification)
7.  [Backend Technical Specification](#7-backend-technical-specification)
8.  [Core Features & User Flows](#8-core-features--user-flows)
9.  [UI/UX Design System](#9-uiux-design-system)
10. [Development Setup Guide](#10-development-setup-guide)
11. [Deployment Guide](#11-deployment-guide)
12. [Security Implementation](#12-security-implementation)
13. [Third-Party Integration Guide](#13-third-party-integration-guide)
14. [Scalability and Performance](#14-scalability-and-performance)

---

## 1. Executive Summary

ChewyAI is an AI-powered study material generation SaaS platform that allows users to upload documents (PDF, DOCX, TXT, PPTX) and generate flashcards and quizzes using AI. The platform features a subscription-based credit system, embedded document viewing, and interactive study interfaces.

### Key Features
- Document upload and embedded viewing with direct AI generation buttons
- Multi-document selection for AI content generation
- Four quiz question types: multiple choice, short answer, select all that apply, true/false
- Flashcard interface with keyboard navigation and flagging
- Credit-based AI generation system with flexible pricing
- Dark space and purple theme with Material UI design
- Manual content creation (free) vs AI generation (paid)

### Technology Stack
- **Frontend:** ReactJS with TypeScript, TailwindCSS, react-icons
- **Backend:** ExpressJS with TypeScript, bcrypt for password hashing
- **Database & Auth:** Supabase (PostgreSQL + Authentication)
- **Payments:** Stripe API for subscriptions and credit purchases
- **AI:** OpenRouter LLM API (Gemini-2.5-Pro)
- **Deployment:** Monorepo structure, ExpressJS serving React build in production

---

## 2. System Architecture

### Project Structure
```
chewy-ai/
├── frontend/                     # React + TypeScript
│   ├── src/
│   ├── package.json
│   ├── tsconfig.json
│   ├── tailwind.config.js
│   └── vite.config.ts
├── backend/                      # Express + TypeScript
│   ├── src/
│   ├── public/                  # Frontend build output
│   ├── package.json
│   ├── tsconfig.json
│   └── .env
├── shared/
│   └── types.ts                 # Shared TypeScript types
├── package.json                 # Root package.json
└── README.md
```

### System Components

```mermaid
graph TB
    A[User] --> B[ReactJS Frontend]
    B --> C[ExpressJS Backend API]
    C --> D[Supabase Database + Storage]
    C --> E[OpenRouter AI API]
    C --> F[Stripe API]
    
    G[Production] --> H[Single ExpressJS Server]
    H --> I[Serves API Routes /api/*]
    H --> J[Serves React Build Static Files /*]
    
    subgraph "Secure Backend-Only Integrations"
        K[Supabase Client]
        L[Stripe Client]
        M[OpenRouter Client]
        N[File Processing]
    end
    
    C --> K
    C --> L
    C --> M
    C --> N
    
    subgraph "Frontend Security"
        O[Frontend only calls Backend API]
        P[No direct external service access]
        Q[All credentials server-side only]
    end
    
    B --> O
```

### Data Flow
1. User uploads documents through React frontend
2. Backend processes and stores documents with extracted text
3. User views documents in embedded viewer with AI generation buttons
4. User selects documents and triggers AI generation
5. Backend checks credits, combines document content, calls OpenRouter API
6. AI-generated content stored in database, credits deducted
7. User studies with flashcard/quiz interfaces

---

## 3. Business Model & Pricing

### Credit System
- **1 credit = 1 AI generation request** (regardless of output size)
- **Cost basis:** $0.04 per OpenRouter request with 400% markup
- **Flexible pricing:** Configurable credit costs per feature type through database

### Subscription Tiers
| Tier | Price | Credits | Study Sets | Quizzes | Additional Features |
|------|-------|---------|------------|---------|-------------------|
| Free | $0 | 10/month | Max 3 | Max 3 | Manual creation unlimited |
| Basic | $9.99/month | 100 | Unlimited | Unlimited | Priority support |
| Pro | $19.99/month | 500 | Unlimited | Unlimited | Advanced analytics |

### Credit Packages (À la carte)
- 25 credits: $4.99 ($0.20/credit)
- 50 credits: $8.99 ($0.18/credit)
- 100 credits: $15.99 ($0.16/credit)

### Credit Configuration Table
```sql
-- Enables dynamic pricing changes
CREATE TABLE ai_operation_costs (
    id UUID PRIMARY KEY,
    operation_type VARCHAR(50) NOT NULL, -- 'flashcard_generation', 'quiz_generation'
    credits_required INTEGER NOT NULL,
    created_at TIMESTAMP DEFAULT NOW(),
    is_active BOOLEAN DEFAULT TRUE
);
```

---

## 4. Database Design

### Enhanced Schema (Supabase/PostgreSQL)

```mermaid
erDiagram
    users {
        uuid id PK
        string email
        string name
        string password_hash
        timestamp created_at
        timestamp updated_at
        string subscription_tier
        integer credits_remaining
        timestamp subscription_expires_at
        string stripe_customer_id
    }
    
    documents {
        uuid id PK
        uuid user_id FK
        string filename
        string file_type
        text content_text
        string file_path
        string file_url
        integer file_size
        timestamp uploaded_at
    }
    
    study_sets {
        uuid id PK
        uuid user_id FK
        string name
        string type
        timestamp created_at
        timestamp updated_at
        boolean is_ai_generated
        jsonb source_documents
    }
    
    flashcards {
        uuid id PK
        uuid study_set_id FK
        text front
        text back
        boolean is_flagged
        timestamp created_at
        boolean is_ai_generated
    }
    
    quiz_questions {
        uuid id PK
        uuid study_set_id FK
        text question_text
        string question_type
        jsonb options
        jsonb correct_answers
        text explanation
        timestamp created_at
        boolean is_ai_generated
    }
    
    credit_transactions {
        uuid id PK
        uuid user_id FK
        integer credits_used
        string operation_type
        string description
        timestamp created_at
        jsonb metadata
    }
    
    ai_operation_costs {
        uuid id PK
        string operation_type
        integer credits_required
        timestamp created_at
        boolean is_active
    }
    
    users ||--o{ documents : uploads
    users ||--o{ study_sets : creates
    users ||--o{ credit_transactions : has
    study_sets ||--o{ flashcards : contains
    study_sets ||--o{ quiz_questions : contains
```

### Key Database Features
- **Flexible credit pricing** through `ai_operation_costs` table
- **Document metadata** for embedded viewer
- **Source tracking** for AI-generated content
- **Credit transaction logging** for transparency

---

## 5. API Specification

### Authentication (Supabase)
- `POST /api/auth/signup` - Register new user
- `POST /api/auth/login` - Authenticate user
- `POST /api/auth/logout` - Logout user
- `GET /api/auth/user` - Get current user profile

### Document Management
- `POST /api/documents/upload` - Upload document with file processing
- `GET /api/documents` - Get user's documents with metadata
- `GET /api/documents/:id` - Get specific document with content
- `GET /api/documents/:id/view` - Serve document for embedded viewer
- `DELETE /api/documents/:id` - Delete document

### Study Sets
- `POST /api/study-sets` - Create study set (manual or AI)
- `GET /api/study-sets` - Get user's study sets
- `GET /api/study-sets/:id` - Get study set with content
- `PUT /api/study-sets/:id` - Update study set
- `DELETE /api/study-sets/:id` - Delete study set

### AI Generation
- `POST /api/ai/generate-flashcards`
  ```json
  {
    "document_ids": ["uuid1", "uuid2"],
    "custom_prompt": "Focus on key concepts",
    "count": 10
  }
  ```
- `POST /api/ai/generate-quiz`
  ```json
  {
    "document_ids": ["uuid1", "uuid2"],
    "question_types": ["multiple_choice", "true_false"],
    "custom_prompt": "Focus on practical applications",
    "count": 5
  }
  ```
- `POST /api/ai/generate-more/:study_set_id` - Add more AI content to existing set

### Credit Management
- `GET /api/credits/balance` - Get current credit balance
- `GET /api/credits/history` - Get credit transaction history
- `GET /api/credits/pricing` - Get current AI operation costs

### Subscription & Payments
- `POST /api/stripe/create-subscription` - Create Stripe subscription
- `POST /api/stripe/purchase-credits` - Purchase credit package
- `POST /api/stripe/webhook` - Handle Stripe webhooks
- `GET /api/subscription/status` - Get subscription status

---

## 6. Frontend Technical Specification

### Technology Stack
- **React 18** with **TypeScript**
- **TailwindCSS** for styling with dark space/purple theme
- **react-icons** for consistent iconography
- **React Router** for navigation
- **Zustand** for global state management
- **React Query** for server state and caching
- **Framer Motion** for hover animations and transitions

### Component Architecture
```
src/
├── components/
│   ├── common/
│   │   ├── Button.tsx              # Material UI style with hover effects
│   │   ├── Input.tsx               # Form inputs with validation
│   │   ├── Modal.tsx               # Overlay modals
│   │   ├── Loading.tsx             # Loading spinners
│   │   └── CreditBalance.tsx       # Credit display component
│   ├── documents/
│   │   ├── DocumentUpload.tsx      # Drag & drop upload
│   │   ├── DocumentList.tsx        # Document library view
│   │   ├── DocumentViewer.tsx      # Embedded document viewer
│   │   ├── DocumentSelector.tsx    # Multi-select with checkboxes
│   │   └── AIGenerationPanel.tsx   # AI generation controls
│   ├── study/
│   │   ├── FlashcardViewer.tsx     # Keyboard navigation + flagging
│   │   ├── QuizInterface.tsx       # Multi-question-type support
│   │   ├── StudySetEditor.tsx      # Manual editing + AI augmentation
│   │   └── StudySetList.tsx        # Dashboard overview
│   ├── subscription/
│   │   ├── PricingCard.tsx         # Subscription tiers
│   │   ├── CreditPurchase.tsx      # Credit package selection
│   │   └── SubscriptionManager.tsx # Billing management
│   └── layout/
│       ├── Header.tsx              # Navigation with credit balance
│       ├── Sidebar.tsx             # App navigation
│       └── Layout.tsx              # Main layout wrapper
├── pages/
│   ├── LandingPage.tsx             # Marketing + signup
│   ├── DashboardPage.tsx           # User home
│   ├── DocumentsPage.tsx           # Document management
│   ├── StudyPage.tsx               # Study interface
│   ├── SubscriptionPage.tsx        # Billing management
│   └── SettingsPage.tsx            # User settings
├── hooks/
│   ├── useAuth.tsx                 # Authentication state
│   ├── useCredits.tsx              # Credit management
│   ├── useDocuments.tsx            # Document operations
│   └── useAIGeneration.tsx         # AI generation state
├── services/
│   ├── api.ts                      # API client
│   ├── auth.ts                     # Authentication service
│   └── stripe.ts                   # Stripe integration
├── stores/
│   └── authStore.ts                # Zustand global state
├── types/
│   └── index.ts                    # TypeScript definitions
└── utils/
    ├── constants.ts                # App constants
    └── helpers.ts                  # Utility functions
```

### Design System
- **Color Palette:** Dark space background (#0a0a0a) with purple accents (#8b5cf6, #a855f7)
- **Typography:** Clean, readable fonts with proper hierarchy
- **Interactive Elements:** Cursor pointer on hover, subtle animations
- **Material UI Inspiration:** Elevated cards, smooth transitions, consistent spacing

### Key UI Components

#### Document Viewer with AI Generation
```typescript
interface DocumentViewerProps {
  documentId: string;
  onGenerateFlashcards: () => void;
  onGenerateQuiz: () => void;
}

// Features:
// - Embedded PDF/document viewer
// - Floating action buttons for AI generation
// - Quick generation with current document context
```

#### Multi-Document Selector
```typescript
interface DocumentSelectorProps {
  documents: Document[];
  selectedIds: string[];
  onSelectionChange: (ids: string[]) => void;
}

// Features:
// - Vertical scrollable list
// - Checkboxes for multi-selection
// - Document metadata display
```

#### Flashcard Interface
```typescript
interface FlashcardViewerProps {
  flashcards: Flashcard[];
  onFlag: (id: string) => void;
}

// Features:
// - Keyboard navigation (Space: flip, Arrow keys: navigate)
// - Flag toggle for difficult cards
// - Always show front when changing cards
// - Smooth flip animations
```

---

## 7. Backend Technical Specification

### Technology Stack
- **Express.js** with **TypeScript**
- **Supabase Client** for database and auth
- **bcrypt** for password hashing
- **Stripe** for payment processing
- **Multer** for file uploads
- **pdf-parse**, **mammoth.js**, **node-xlsx** for document processing

### Service Architecture
```
src/
├── api/
│   ├── routes/
│   │   ├── auth.ts                 # Authentication routes
│   │   ├── documents.ts            # Document management
│   │   ├── studySets.ts           # Study set operations
│   │   ├── ai.ts                  # AI generation endpoints
│   │   ├── credits.ts             # Credit management
│   │   └── stripe.ts              # Payment processing
│   ├── controllers/
│   │   ├── authController.ts       # Auth business logic
│   │   ├── documentController.ts   # Document operations
│   │   ├── aiController.ts        # AI generation logic
│   │   └── stripeController.ts    # Payment logic
│   └── middleware/
│       ├── auth.ts                # JWT authentication
│       ├── creditCheck.ts         # Credit validation
│       ├── rateLimit.ts           # API rate limiting
│       └── validation.ts          # Input validation
├── services/
│   ├── supabaseService.ts         # Database operations
│   ├── aiService.ts               # OpenRouter integration
│   ├── documentService.ts         # File processing
│   ├── creditService.ts           # Credit management
│   └── stripeService.ts           # Payment processing
├── utils/
│   ├── fileProcessor.ts           # Document text extraction
│   ├── creditCalculator.ts        # Dynamic credit pricing
│   └── validators.ts              # Input validation schemas
├── types/
│   └── index.ts                   # Shared type definitions
├── config/
│   ├── database.ts                # Supabase configuration
│   ├── stripe.ts                  # Stripe configuration
│   └── environment.ts             # Environment variables
└── app.ts                         # Express app setup
```

### Key Backend Features

#### Dynamic Credit Pricing
```typescript
// Flexible credit system that can be updated without code changes
class CreditService {
  async getOperationCost(operationType: string): Promise<number> {
    const cost = await supabase
      .from('ai_operation_costs')
      .select('credits_required')
      .eq('operation_type', operationType)
      .eq('is_active', true)
      .single();
    
    return cost?.credits_required || 1; // Default to 1 credit
  }
  
  async deductCredits(userId: string, operation: string): Promise<boolean> {
    const cost = await this.getOperationCost(operation);
    // Implement credit deduction with transaction logging
  }
}
```

#### Document Processing Pipeline
```typescript
// Multi-format document processing
class DocumentProcessor {
  async processFile(file: Express.Multer.File): Promise<ProcessedDocument> {
    switch (file.mimetype) {
      case 'application/pdf':
        return this.processPDF(file);
      case 'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
        return this.processDocx(file);
      case 'text/plain':
        return this.processText(file);
      case 'application/vnd.openxmlformats-officedocument.presentationml.presentation':
        return this.processPPTX(file);
      default:
        throw new Error('Unsupported file type');
    }
  }
}
```

---

## 8. Core Features & User Flows

### Primary User Flows

#### 1. Document Upload & Management Flow
```mermaid
flowchart TD
    A[User visits Documents page] --> B[Drag & drop or click upload]
    B --> C[Select PDF/DOCX/TXT/PPTX file]
    C --> D[Backend processes file]
    D --> E[Extract text content]
    E --> F[Store document + metadata]
    F --> G[Show in document library]
    G --> H[Click document to view]
    H --> I[Embedded viewer with AI buttons]
```

#### 2. AI Generation Flow
```mermaid
flowchart TD
    A[User clicks Generate Flashcards/Quiz] --> B[Multi-document selector opens]
    B --> C[Select documents with checkboxes]
    C --> D[Choose generation options]
    D --> E[Enter custom prompt (optional)]
    E --> F[Submit generation request]
    F --> G{Check credits}
    G -->|Insufficient| H[Show upgrade/purchase modal]
    G -->|Sufficient| I[Combine document content]
    I --> J[Send to OpenRouter API]
    J --> K[Parse AI response]
    K --> L[Store generated content]
    L --> M[Deduct credits]
    M --> N[Redirect to study interface]
```

#### 3. Study Flow
```mermaid
flowchart TD
    A[User selects study set] --> B{Content type}
    B -->|Flashcards| C[Flashcard viewer]
    B -->|Quiz| D[Quiz interface]
    
    C --> E[Show front of card]
    E --> F[Space bar to flip]
    F --> G[Arrow keys to navigate]
    G --> H[Flag difficult cards]
    
    D --> I[Show question]
    I --> J[User selects answer]
    J --> K[Immediate feedback]
    K --> L[Track score]
```

### Feature Specifications

#### Flashcard Interface
- **Keyboard Navigation:**
  - Space bar: Flip card
  - Left/Right arrows: Navigate between cards
  - F key: Flag/unflag current card
- **Visual State:**
  - Always show front when navigating to new card
  - Smooth flip animation using CSS transforms
  - Flag indicator visible when card is flagged
  - Progress indicator showing position in set

#### Quiz Interface
- **Question Types:**
  1. **Multiple Choice:** Single correct answer from 4 options
  2. **Select All That Apply:** Multiple correct answers
  3. **True/False:** Binary choice with explanations
  4. **Short Answer:** Text input with keyword matching
- **Features:**
  - Immediate feedback after each question
  - Optional timer per question
  - Progress tracking throughout quiz
  - Final score summary with review option

#### Document Viewer Integration
- **Embedded Viewer:** PDF.js or similar for in-app viewing
- **Generation Buttons:** Floating action buttons over viewer
- **Quick Generation:** Single-document generation with one click
- **Context Preservation:** Maintain viewer position during generation

---

## 9. UI/UX Design System

### Theme Configuration
```typescript
// Tailwind theme extension
const theme = {
  colors: {
    primary: {
      50: '#faf5ff',
      500: '#8b5cf6',
      600: '#7c3aed',
      700: '#6d28d9',
      900: '#4c1d95'
    },
    background: {
      primary: '#0a0a0a',
      secondary: '#1a1a1a',
      tertiary: '#2a2a2a'
    },
    text: {
      primary: '#ffffff',
      secondary: '#a3a3a3',
      muted: '#737373'
    }
  }
}
```

### Component Design Principles
- **Material UI Inspiration:** Elevated surfaces, consistent shadows
- **Interactive Feedback:** 
  - `cursor-pointer` on all clickable elements
  - Hover state changes (opacity, scale, color)
  - Focus states for accessibility
- **Consistent Spacing:** 8px grid system
- **Typography Scale:** Clear hierarchy with proper contrast

### Animation Guidelines
```css
/* Hover effects for buttons */
.btn-primary {
  @apply transition-all duration-200 ease-in-out;
  @apply hover:scale-105 hover:shadow-lg;
}

/* Card hover effects */
.card {
  @apply transition-transform duration-200;
  @apply hover:translate-y-[-2px];
}

/* Flashcard flip animation */
.flashcard {
  @apply transition-transform duration-300;
  transform-style: preserve-3d;
}
```

---

## 10. Development Setup Guide

### Prerequisites
- Node.js (v18 or later)
- npm or yarn
- Git
- Supabase account
- Stripe account
- OpenRouter API key

### Environment Configuration
```bash
# Backend .env (All sensitive credentials stay server-side)
SUPABASE_URL=your_supabase_project_url
SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_KEY=your_supabase_service_role_key
STRIPE_SECRET_KEY=your_stripe_secret_key
STRIPE_WEBHOOK_SECRET=your_stripe_webhook_secret
STRIPE_PUBLISHABLE_KEY=your_stripe_publishable_key
OPENROUTER_API_KEY=your_openrouter_api_key
JWT_SECRET=your_jwt_secret_key
PORT=5000
NODE_ENV=development

# Frontend .env (Only backend API endpoint)
VITE_API_BASE_URL=http://localhost:5000/api
```

### Setup Steps
1. **Clone repository:**
   ```bash
   git clone <repository_url>
   cd chewy-ai
   ```

2. **Install dependencies:**
   ```bash
   # Root dependencies
   npm install
   
   # Backend dependencies
   cd backend && npm install
   
   # Frontend dependencies
   cd ../frontend && npm install
   ```

3. **Database setup:**
   - Create Supabase project
   - Run migration scripts
   - Set up authentication policies

4. **Development servers:**
   ```bash
   # Start backend (from /backend)
   npm run dev
   
   # Start frontend (from /frontend)
   npm run dev
   ```

### Build Process
```bash
# Production build
npm run build

# This will:
# 1. Build frontend with Vite
# 2. Copy build output to /backend/public
# 3. Build backend TypeScript
# 4. Create single deployable server
```

---

## 11. Deployment Guide

### Production Architecture
- Single ExpressJS server serving both API and static files
- Environment-based configuration
- Docker containerization
- Automated build pipeline

### Production Deployment (No Docker)
```bash
# Simple Node.js deployment
# 1. Build the application
npm run build

# 2. Start the production server
npm start
```

### Deployment Steps
1. **Environment setup:** Configure production environment variables on server
2. **Install dependencies:** `npm install --production`
3. **Build application:** `npm run build` (builds frontend to backend/public)
4. **Start server:** `npm start` or use PM2 for process management
5. **Configure domain:** Set up reverse proxy (Nginx) and SSL
6. **Monitor:** Set up logging and monitoring tools

---

## 12. Security Implementation

### Authentication & Authorization
- **Supabase Auth:** Row-level security policies
- **JWT Tokens:** Secure token management
- **bcrypt:** Password hashing with salt rounds = 12
- **Session Management:** Secure session handling

### Data Protection
- **HTTPS Enforcement:** All communication encrypted
- **Input Validation:** Comprehensive validation on all endpoints
- **SQL Injection Prevention:** Parameterized queries through Supabase
- **XSS Protection:** Input sanitization and CSP headers

### API Security
```typescript
// Rate limiting example
const rateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
  message: 'Too many requests from this IP'
});

// Credit validation middleware
const creditCheck = async (req: Request, res: Response, next: NextFunction) => {
  const operationType = req.body.operation_type;
  const cost = await creditService.getOperationCost(operationType);
  const userCredits = await creditService.getUserCredits(req.user.id);
  
  if (userCredits < cost) {
    return res.status(402).json({ error: 'Insufficient credits' });
  }
  
  next();
};
```

---

## 13. Third-Party Integration Guide

### OpenRouter AI Integration
```typescript
class AIService {
  private openrouter: OpenRouter;

  constructor() {
    this.openrouter = new OpenRouter({
      apiKey: process.env.OPENROUTER_API_KEY,
      defaultModel: 'google/gemini-2.5-pro'
    });
  }

  async generateFlashcards(content: string, customPrompt?: string): Promise<Flashcard[]> {
    const prompt = customPrompt 
      ? `${customPrompt}\n\nGenerate flashcards from: ${content}`
      : `Generate study flashcards from the following content: ${content}`;

    const response = await this.openrouter.chat.completions.create({
      model: 'google/gemini-2.5-pro',
      messages: [{ role: 'user', content: prompt }],
      max_tokens: 2000
    });

    return this.parseFlashcardResponse(response.choices[0].message.content);
  }
}
```

### Stripe Integration
```typescript
class StripeService {
  private stripe: Stripe;

  async createSubscription(customerId: string, priceId: string) {
    return await this.stripe.subscriptions.create({
      customer: customerId,
      items: [{ price: priceId }],
      payment_behavior: 'default_incomplete',
      expand: ['latest_invoice.payment_intent']
    });
  }

  async purchaseCredits(customerId: string, creditPackage: string) {
    const prices = {
      '25_credits': 'price_25_credits_id',
      '50_credits': 'price_50_credits_id',
      '100_credits': 'price_100_credits_id'
    };

    return await this.stripe.paymentIntents.create({
      amount: this.getCreditPackagePrice(creditPackage),
      currency: 'usd',
      customer: customerId,
      metadata: { credit_package: creditPackage }
    });
  }
}
```

### Document Processing
```typescript
class DocumentProcessor {
  async processPDF(buffer: Buffer): Promise<string> {
    const data = await pdfParse(buffer);
    return data.text;
  }

  async processDocx(buffer: Buffer): Promise<string> {
    const result = await mammoth.extractRawText({ buffer });
    return result.value;
  }

  async processPPTX(buffer: Buffer): Promise<string> {
    // Custom PPTX processing implementation
    return extractTextFromPPTX(buffer);
  }
}
```

---

## 14. Scalability and Performance

### Database Optimization
- **Connection Pooling:** Supabase handles connection management
- **Query Optimization:** Proper indexing on frequently queried fields
- **Read Replicas:** Utilize Supabase read replicas for heavy read operations

### Caching Strategy
- **API Response Caching:** Redis for frequently accessed data
- **Static Asset Caching:** CDN for frontend assets
- **Database Query Caching:** Supabase query caching

### Performance Monitoring
- **API Metrics:** Response times, error rates
- **Credit Usage Tracking:** Monitor AI generation costs
- **User Behavior Analytics:** Study patterns and feature usage

### Scaling Considerations
- **Horizontal Scaling:** Multiple backend instances behind load balancer
- **File Storage:** Move to cloud storage (S3/Supabase Storage) for documents
- **AI Service Limits:** Implement queuing for high-volume AI requests

---

## Development Timeline

### Phase 1: Foundation (Weeks 1-2)
- [ ] Project setup and monorepo structure
- [ ] Supabase database schema and authentication
- [ ] Basic frontend components with dark theme
- [ ] Backend API structure with TypeScript

### Phase 2: Core Features (Weeks 3-4)
- [ ] Document upload and processing
- [ ] Basic study set creation (manual)
- [ ] Flashcard interface with keyboard navigation
- [ ] Credit system implementation

### Phase 3: AI Integration (Weeks 5-6)
- [ ] OpenRouter API integration
- [ ] Multi-document selection UI
- [ ] AI generation workflows
- [ ] Custom prompt functionality

### Phase 4: Advanced Features (Weeks 7-8)
- [ ] Quiz interface with all question types
- [ ] Study set editing and management
- [ ] Embedded document viewer
- [ ] AI generation from document viewer

### Phase 5: Business Logic (Weeks 9-10)
- [ ] Stripe subscription integration
- [ ] Credit purchasing system
- [ ] Subscription tier enforcement
- [ ] User dashboard and analytics

### Phase 6: Polish & Production (Weeks 11-12)
- [ ] UI/UX refinements
- [ ] Performance optimization
- [ ] Production deployment setup
- [ ] Testing and bug fixes

---

This comprehensive PRD provides all the necessary information for one-shot development of ChewyAI, with clear specifications, flexible architecture, and detailed implementation guidance.