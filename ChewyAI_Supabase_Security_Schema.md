# ChewyAI - Supabase Security Schema & RLS Policies

## Database Security Architecture

```mermaid
flowchart TD
    A[User Request] --> B[Backend API]
    B --> C[Supabase Client with Service Key]
    C --> D{RLS Policy Check}
    D -->|Pass| E[Database Operation]
    D -->|Fail| F[Access Denied]
    
    subgraph "Row Level Security"
        G[User Data Isolation]
        H[Resource Ownership]
        I[Operation Restrictions]
    end
    
    subgraph "Stored Procedures"
        J[Credit Deduction]
        K[Subscription Updates]
        L[Audit Logging]
    end
    
    D --> G
    D --> H
    D --> I
    E --> J
    E --> K
    E --> L
```

## Complete Database Schema with Security

### 1. Users Table
```sql
-- Users table with authentication integration
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) UNIQUE NOT NULL,
    name VARCHAR(255),
    password_hash VARCHAR(255) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    subscription_tier VARCHAR(50) DEFAULT 'free' CHECK (subscription_tier IN ('free', 'basic', 'pro')),
    credits_remaining INTEGER DEFAULT 10 CHECK (credits_remaining >= 0),
    subscription_expires_at TIMESTAMP WITH TIME ZONE,
    stripe_customer_id VARCHAR(255),
    last_login TIMESTAMP WITH TIME ZONE,
    is_active BOOLEAN DEFAULT TRUE
);

-- RLS Policies for users table
ALTER TABLE users ENABLE ROW LEVEL SECURITY;

-- Users can only read their own profile
CREATE POLICY "Users can read own profile" ON users
    FOR SELECT USING (auth.uid() = id);

-- Users can update their own non-critical fields
CREATE POLICY "Users can update own profile" ON users
    FOR UPDATE USING (auth.uid() = id)
    WITH CHECK (
        auth.uid() = id AND
        -- Prevent direct manipulation of critical fields
        OLD.credits_remaining = NEW.credits_remaining AND
        OLD.subscription_tier = NEW.subscription_tier AND
        OLD.stripe_customer_id = NEW.stripe_customer_id
    );

-- Only system can insert users (registration process)
CREATE POLICY "System can insert users" ON users
    FOR INSERT WITH CHECK (true);
```

### 2. Documents Table
```sql
CREATE TABLE documents (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    filename VARCHAR(255) NOT NULL,
    file_type VARCHAR(10) NOT NULL CHECK (file_type IN ('pdf', 'docx', 'txt', 'pptx')),
    file_size INTEGER NOT NULL CHECK (file_size > 0 AND file_size <= 50000000), -- 50MB limit
    content_text TEXT,
    supabase_storage_path VARCHAR(500) NOT NULL,
    uploaded_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_processed BOOLEAN DEFAULT FALSE,
    processing_error TEXT
);

-- Indexes for performance
CREATE INDEX idx_documents_user_id ON documents(user_id);
CREATE INDEX idx_documents_uploaded_at ON documents(uploaded_at DESC);

-- RLS Policies for documents
ALTER TABLE documents ENABLE ROW LEVEL SECURITY;

-- Users can only access their own documents
CREATE POLICY "Users own documents access" ON documents
    FOR ALL USING (auth.uid() = user_id);

-- Additional security: Prevent unauthorized inserts
CREATE POLICY "Users can only insert own documents" ON documents
    FOR INSERT WITH CHECK (auth.uid() = user_id);
```

### 3. Study Sets Table
```sql
CREATE TABLE study_sets (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    type VARCHAR(20) NOT NULL CHECK (type IN ('flashcards', 'quiz', 'mixed')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_ai_generated BOOLEAN DEFAULT FALSE,
    source_documents JSONB, -- Array of document IDs used for generation
    custom_prompt TEXT,
    total_items INTEGER DEFAULT 0,
    last_studied_at TIMESTAMP WITH TIME ZONE
);

-- Indexes
CREATE INDEX idx_study_sets_user_id ON study_sets(user_id);
CREATE INDEX idx_study_sets_type ON study_sets(type);
CREATE INDEX idx_study_sets_updated_at ON study_sets(updated_at DESC);

-- RLS Policies
ALTER TABLE study_sets ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users own study sets access" ON study_sets
    FOR ALL USING (auth.uid() = user_id);

-- Trigger to update total_items count
CREATE OR REPLACE FUNCTION update_study_set_item_count()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_TABLE_NAME = 'flashcards' THEN
        UPDATE study_sets 
        SET total_items = (
            SELECT COUNT(*) FROM flashcards WHERE study_set_id = COALESCE(NEW.study_set_id, OLD.study_set_id)
        )
        WHERE id = COALESCE(NEW.study_set_id, OLD.study_set_id);
    ELSIF TG_TABLE_NAME = 'quiz_questions' THEN
        UPDATE study_sets 
        SET total_items = (
            SELECT COUNT(*) FROM quiz_questions WHERE study_set_id = COALESCE(NEW.study_set_id, OLD.study_set_id)
        )
        WHERE id = COALESCE(NEW.study_set_id, OLD.study_set_id);
    END IF;
    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;
```

### 4. Flashcards Table
```sql
CREATE TABLE flashcards (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    study_set_id UUID NOT NULL REFERENCES study_sets(id) ON DELETE CASCADE,
    front TEXT NOT NULL,
    back TEXT NOT NULL,
    is_flagged BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_ai_generated BOOLEAN DEFAULT FALSE,
    difficulty_level INTEGER DEFAULT 1 CHECK (difficulty_level BETWEEN 1 AND 5),
    times_reviewed INTEGER DEFAULT 0,
    last_reviewed_at TIMESTAMP WITH TIME ZONE
);

-- Indexes
CREATE INDEX idx_flashcards_study_set_id ON flashcards(study_set_id);
CREATE INDEX idx_flashcards_flagged ON flashcards(is_flagged) WHERE is_flagged = TRUE;

-- RLS Policies - Inherit from study_set ownership
ALTER TABLE flashcards ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users access flashcards through study sets" ON flashcards
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM study_sets 
            WHERE study_sets.id = flashcards.study_set_id 
            AND study_sets.user_id = auth.uid()
        )
    );

-- Trigger for item count updates
CREATE TRIGGER update_flashcard_count
    AFTER INSERT OR DELETE ON flashcards
    FOR EACH ROW EXECUTE FUNCTION update_study_set_item_count();
```

### 5. Quiz Questions Table
```sql
CREATE TABLE quiz_questions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    study_set_id UUID NOT NULL REFERENCES study_sets(id) ON DELETE CASCADE,
    question_text TEXT NOT NULL,
    question_type VARCHAR(20) NOT NULL CHECK (question_type IN ('multiple_choice', 'short_answer', 'select_all', 'true_false')),
    options JSONB, -- Array of options for multiple choice/select all
    correct_answers JSONB NOT NULL, -- Array of correct answer indices or text
    explanation TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_ai_generated BOOLEAN DEFAULT FALSE,
    difficulty_level INTEGER DEFAULT 1 CHECK (difficulty_level BETWEEN 1 AND 5),
    times_attempted INTEGER DEFAULT 0,
    times_correct INTEGER DEFAULT 0
);

-- Indexes
CREATE INDEX idx_quiz_questions_study_set_id ON quiz_questions(study_set_id);
CREATE INDEX idx_quiz_questions_type ON quiz_questions(question_type);

-- RLS Policies
ALTER TABLE quiz_questions ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users access quiz questions through study sets" ON quiz_questions
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM study_sets 
            WHERE study_sets.id = quiz_questions.study_set_id 
            AND study_sets.user_id = auth.uid()
        )
    );

-- Trigger for item count updates
CREATE TRIGGER update_quiz_question_count
    AFTER INSERT OR DELETE ON quiz_questions
    FOR EACH ROW EXECUTE FUNCTION update_study_set_item_count();
```

### 6. Credit Transactions Table
```sql
CREATE TABLE credit_transactions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    credits_used INTEGER NOT NULL,
    operation_type VARCHAR(50) NOT NULL,
    description TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    metadata JSONB DEFAULT '{}',
    study_set_id UUID REFERENCES study_sets(id), -- Optional reference to what was generated
    ip_address INET,
    user_agent TEXT
);

-- Indexes for audit queries
CREATE INDEX idx_credit_transactions_user_id ON credit_transactions(user_id);
CREATE INDEX idx_credit_transactions_created_at ON credit_transactions(created_at DESC);
CREATE INDEX idx_credit_transactions_operation_type ON credit_transactions(operation_type);

-- RLS Policies - Read-only for users
ALTER TABLE credit_transactions ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can read own credit history" ON credit_transactions
    FOR SELECT USING (auth.uid() = user_id);

-- No UPDATE or DELETE policies - append-only table
CREATE POLICY "No updates to credit transactions" ON credit_transactions
    FOR UPDATE USING (FALSE);

CREATE POLICY "No deletes from credit transactions" ON credit_transactions
    FOR DELETE USING (FALSE);
```

### 7. AI Operation Costs Table (Admin Configuration)
```sql
CREATE TABLE ai_operation_costs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    operation_type VARCHAR(50) NOT NULL UNIQUE,
    credits_required INTEGER NOT NULL CHECK (credits_required > 0),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_active BOOLEAN DEFAULT TRUE,
    description TEXT
);

-- Insert default operation costs
INSERT INTO ai_operation_costs (operation_type, credits_required, description) VALUES
('flashcard_generation', 1, 'Generate flashcards from documents'),
('quiz_generation', 1, 'Generate quiz questions from documents'),
('content_expansion', 2, 'Expand existing study materials'),
('content_summary', 1, 'Summarize document content');

-- RLS Policies - Read-only for all authenticated users
ALTER TABLE ai_operation_costs ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Authenticated users can read operation costs" ON ai_operation_costs
    FOR SELECT USING (auth.role() = 'authenticated');

-- Only admin can modify
CREATE POLICY "Only admin can modify operation costs" ON ai_operation_costs
    FOR ALL USING (auth.jwt() ->> 'role' = 'admin');
```

## Critical Stored Procedures

### 1. Atomic Credit Deduction
```sql
CREATE OR REPLACE FUNCTION deduct_credits(
    p_user_id UUID,
    p_credits_to_deduct INTEGER,
    p_operation_type TEXT,
    p_description TEXT DEFAULT NULL,
    p_metadata JSONB DEFAULT '{}',
    p_study_set_id UUID DEFAULT NULL
)
RETURNS TABLE(success BOOLEAN, remaining_credits INTEGER, message TEXT) AS $$
DECLARE
    current_credits INTEGER;
    operation_cost INTEGER;
    final_description TEXT;
BEGIN
    -- Validate operation type and get cost
    SELECT credits_required INTO operation_cost
    FROM ai_operation_costs 
    WHERE operation_type = p_operation_type AND is_active = TRUE;
    
    IF operation_cost IS NULL THEN
        RETURN QUERY SELECT FALSE, 0, 'Invalid operation type';
        RETURN;
    END IF;
    
    -- Use the configured cost if no specific amount provided
    IF p_credits_to_deduct IS NULL THEN
        p_credits_to_deduct := operation_cost;
    END IF;
    
    -- Get current credits with row lock to prevent race conditions
    SELECT credits_remaining INTO current_credits
    FROM users 
    WHERE id = p_user_id
    FOR UPDATE;
    
    -- Check if user exists
    IF current_credits IS NULL THEN
        RETURN QUERY SELECT FALSE, 0, 'User not found';
        RETURN;
    END IF;
    
    -- Check if sufficient credits
    IF current_credits < p_credits_to_deduct THEN
        RETURN QUERY SELECT FALSE, current_credits, 'Insufficient credits';
        RETURN;
    END IF;
    
    -- Deduct credits atomically
    UPDATE users 
    SET credits_remaining = credits_remaining - p_credits_to_deduct,
        updated_at = NOW()
    WHERE id = p_user_id;
    
    -- Set description
    final_description := COALESCE(p_description, 'AI generation: ' || p_operation_type);
    
    -- Log transaction
    INSERT INTO credit_transactions (
        user_id, 
        credits_used, 
        operation_type, 
        description, 
        metadata,
        study_set_id
    ) VALUES (
        p_user_id, 
        p_credits_to_deduct, 
        p_operation_type, 
        final_description,
        p_metadata,
        p_study_set_id
    );
    
    -- Return success with new balance
    RETURN QUERY SELECT TRUE, (current_credits - p_credits_to_deduct), 'Credits deducted successfully';
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

### 2. Credit Addition (for purchases/subscriptions)
```sql
CREATE OR REPLACE FUNCTION add_credits(
    p_user_id UUID,
    p_credits_to_add INTEGER,
    p_source TEXT, -- 'purchase', 'subscription', 'bonus', etc.
    p_reference_id TEXT DEFAULT NULL -- Stripe payment intent ID, etc.
)
RETURNS TABLE(success BOOLEAN, new_balance INTEGER, message TEXT) AS $$
DECLARE
    new_credits INTEGER;
BEGIN
    -- Validate inputs
    IF p_credits_to_add <= 0 THEN
        RETURN QUERY SELECT FALSE, 0, 'Invalid credit amount';
        RETURN;
    END IF;
    
    -- Add credits atomically
    UPDATE users 
    SET credits_remaining = credits_remaining + p_credits_to_add,
        updated_at = NOW()
    WHERE id = p_user_id
    RETURNING credits_remaining INTO new_credits;
    
    -- Check if user exists
    IF new_credits IS NULL THEN
        RETURN QUERY SELECT FALSE, 0, 'User not found';
        RETURN;
    END IF;
    
    -- Log transaction (negative credits_used indicates addition)
    INSERT INTO credit_transactions (
        user_id, 
        credits_used, 
        operation_type, 
        description,
        metadata
    ) VALUES (
        p_user_id, 
        -p_credits_to_add, -- Negative to indicate addition
        'credit_addition',
        'Credits added: ' || p_source,
        jsonb_build_object('source', p_source, 'reference_id', p_reference_id)
    );
    
    RETURN QUERY SELECT TRUE, new_credits, 'Credits added successfully';
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

### 3. Subscription Management
```sql
CREATE OR REPLACE FUNCTION update_subscription(
    p_user_id UUID,
    p_tier VARCHAR(50),
    p_stripe_customer_id VARCHAR(255),
    p_expires_at TIMESTAMP WITH TIME ZONE,
    p_credits_to_add INTEGER DEFAULT 0
)
RETURNS TABLE(success BOOLEAN, message TEXT) AS $$
BEGIN
    -- Validate tier
    IF p_tier NOT IN ('free', 'basic', 'pro') THEN
        RETURN QUERY SELECT FALSE, 'Invalid subscription tier';
        RETURN;
    END IF;
    
    -- Update subscription
    UPDATE users 
    SET subscription_tier = p_tier,
        stripe_customer_id = p_stripe_customer_id,
        subscription_expires_at = p_expires_at,
        credits_remaining = CASE 
            WHEN p_credits_to_add > 0 THEN credits_remaining + p_credits_to_add
            ELSE credits_remaining
        END,
        updated_at = NOW()
    WHERE id = p_user_id;
    
    -- Log credit addition if any
    IF p_credits_to_add > 0 THEN
        INSERT INTO credit_transactions (
            user_id, 
            credits_used, 
            operation_type, 
            description,
            metadata
        ) VALUES (
            p_user_id, 
            -p_credits_to_add,
            'subscription_credits',
            'Monthly credits: ' || p_tier,
            jsonb_build_object('tier', p_tier, 'expires_at', p_expires_at)
        );
    END IF;
    
    RETURN QUERY SELECT TRUE, 'Subscription updated successfully';
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

### 4. User Statistics Function
```sql
CREATE OR REPLACE FUNCTION get_user_stats(p_user_id UUID)
RETURNS TABLE(
    total_documents INTEGER,
    total_study_sets INTEGER,
    total_flashcards INTEGER,
    total_quiz_questions INTEGER,
    credits_used_this_month INTEGER,
    last_activity TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        (SELECT COUNT(*)::INTEGER FROM documents WHERE user_id = p_user_id),
        (SELECT COUNT(*)::INTEGER FROM study_sets WHERE user_id = p_user_id),
        (SELECT COUNT(*)::INTEGER FROM flashcards f 
         JOIN study_sets s ON f.study_set_id = s.id 
         WHERE s.user_id = p_user_id),
        (SELECT COUNT(*)::INTEGER FROM quiz_questions q 
         JOIN study_sets s ON q.study_set_id = s.id 
         WHERE s.user_id = p_user_id),
        (SELECT COALESCE(SUM(credits_used), 0)::INTEGER 
         FROM credit_transactions 
         WHERE user_id = p_user_id 
         AND created_at >= date_trunc('month', CURRENT_DATE)),
        (SELECT GREATEST(
            COALESCE(MAX(last_studied_at), '1970-01-01'::timestamp),
            COALESCE(MAX(uploaded_at), '1970-01-01'::timestamp),
            COALESCE(MAX(created_at), '1970-01-01'::timestamp)
         ) FROM (
            SELECT last_studied_at, NULL::timestamp as uploaded_at, NULL::timestamp as created_at FROM study_sets WHERE user_id = p_user_id
            UNION ALL
            SELECT NULL::timestamp, uploaded_at, NULL::timestamp FROM documents WHERE user_id = p_user_id
            UNION ALL
            SELECT NULL::timestamp, NULL::timestamp, created_at FROM credit_transactions WHERE user_id = p_user_id
         ) activities);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

## Security Features Summary

### ✅ Row Level Security (RLS)
- **Complete data isolation** between users
- **Inheritance security** for related tables (flashcards inherit study_set ownership)
- **Append-only audit tables** prevent tampering
- **Admin-only configuration** for sensitive settings

### ✅ Stored Procedures
- **Atomic transactions** prevent race conditions
- **Input validation** at database level
- **Audit logging** for all operations
- **SECURITY DEFINER** ensures consistent permissions

### ✅ Data Integrity
- **Check constraints** prevent invalid data
- **Foreign key cascades** maintain referential integrity
- **Triggers** for automatic calculations
- **Indexes** for performance

### ✅ Access Control
- **Granular permissions** per operation type
- **Resource ownership verification**
- **No direct credit manipulation** by users
- **Secure function execution**

This schema ensures that even if someone gains database access, they cannot:
- Access other users' data
- Manipulate credits directly
- Bypass audit logging
- Corrupt data integrity